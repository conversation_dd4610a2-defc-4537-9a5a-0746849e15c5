// <auto-generated />
// Code generated by protoc-gen-rpc-wrap. DO NOT EDIT.
// versions:
// - protoc-gen-rpc-wrap v1.0.0
// - protoc             v3.16.0
// source: BattleService.proto

using System.Buffers;
using BattleServer.Service;

namespace BattleServer.Nats.SerializerRegistry;

public partial class ProtoBufSerializer<T>
{
	private static readonly Dictionary<Type, Func<ReadOnlySequence<byte>, T>> Parsers = new()
	{
		{ typeof(MergeHeroReq), (buffer) => (T)(object)MergeHeroReq.Parser.ParseFrom(buffer) },
		{ typeof(MergeHeroResp), (buffer) => (T)(object)MergeHeroResp.Parser.ParseFrom(buffer) },
		{ typeof(ReadyBattleReq), (buffer) => (T)(object)ReadyBattleReq.Parser.ParseFrom(buffer) },
		{ typeof(LeaveBattleReq), (buffer) => (T)(object)LeaveBattleReq.Parser.ParseFrom(buffer) },
		{ typeof(LeaveBattleResp), (buffer) => (T)(object)LeaveBattleResp.Parser.ParseFrom(buffer) },
		{ typeof(SelectBufferReq), (buffer) => (T)(object)SelectBufferReq.Parser.ParseFrom(buffer) },
		{ typeof(SelectBufferResp), (buffer) => (T)(object)SelectBufferResp.Parser.ParseFrom(buffer) },
		{ typeof(EndBattleResp), (buffer) => (T)(object)EndBattleResp.Parser.ParseFrom(buffer) },
		{ typeof(CreateBattleReq), (buffer) => (T)(object)CreateBattleReq.Parser.ParseFrom(buffer) },
		{ typeof(CreateBattleResp), (buffer) => (T)(object)CreateBattleResp.Parser.ParseFrom(buffer) },
		{ typeof(ReadyBattleResp), (buffer) => (T)(object)ReadyBattleResp.Parser.ParseFrom(buffer) },
		{ typeof(EndBattleReq), (buffer) => (T)(object)EndBattleReq.Parser.ParseFrom(buffer) },
		{ typeof(EnterBattleReq), (buffer) => (T)(object)EnterBattleReq.Parser.ParseFrom(buffer) },
		{ typeof(EnterBattleResp), (buffer) => (T)(object)EnterBattleResp.Parser.ParseFrom(buffer) },
	};
}
