package player

import (
	"liteframe/internal/common/error_code"
	"liteframe/internal/common/protos/cs"
	"liteframe/internal/common/protos/dbstruct"
	"liteframe/internal/common/protos/public"
	"liteframe/internal/common/rpc_def"
	"liteframe/internal/common/table"
	"liteframe/pkg/log"
)

// Item 道具模块
type Item struct {
	player *Player
}

func NewItem(p *Player) *Item {
	return &Item{
		player: p,
	}
}

// InitDB 初始化模块数据
func (b *Item) InitDB(db *dbstruct.UserDB) {
	if db.Game == nil {
		db.Game = &dbstruct.GameDB{}
	}
	log.Info("Item InitDB")
}

// OnCrossDay 处理跨天逻辑
func (b *Item) OnCrossDay(natural bool, nowUnix int64) {
	// 处理跨天逻辑，如清理过期道具等
	// TODO: 实现跨天逻辑
}

// IsMoneyType 通用接口：判断道具类型
// @Export
func (i *Item) IsMoneyType(itemId int32) bool {
	if itemId <= 0 {
		log.Error("[Item.IsMoneyType] invalid params",
			log.Kv("uid", i.player.Uid()),
			log.Kv("itemId", itemId))
		return false
	}
	return i.player.IsMoneyType(itemId)
}

// IsCanDelItem 通用接口：检查是否可以扣除道具
// @Export
func (i *Item) IsCanDelItem(itemId int32, delMoneyNum int32) bool {
	if itemId <= 0 {
		log.Error("[Player.IsCanDelItem] invalid params",
			log.Kv("uid", i.player.Uid()),
			log.Kv("itemId", itemId),
			log.Kv("delMoneyNum", delMoneyNum))
		return false
	}
	if i.IsMoneyType(itemId) {
		return i.player.IsCanDelMoney(itemId, delMoneyNum)
	} else {
		return i.player.IsBagCanDelItem(itemId, delMoneyNum)
	}
}

// GetItemNumById 通用接口：根据货币 Id 获取数量
// @Export
func (i *Item) GetItemNumById(itemId int32) int32 {
	if itemId <= 0 {
		log.Error("[Player.GetItemNumById] invalid params",
			log.Kv("uid", i.player.Uid()),
			log.Kv("itemId", itemId))
		return -1
	}
	if i.IsMoneyType(itemId) {
		return i.player.GetMoneyNumById(itemId)
	} else {
		return i.player.GetBagItemNumById(itemId)
	}
}

// AddItem 通用接口：添加道具
// @Export
func (i *Item) AddItem(itemId int32, addItemNum int32, operateReason int32) bool {
	if itemId <= 0 || addItemNum <= 0 {
		log.Error("[Player.AddItem] invalid params",
			log.Kv("uid", i.player.Uid()),
			log.Kv("itemId", itemId),
			log.Kv("addItemNum", addItemNum),
			log.Kv("operateReason", operateReason))
		return false
	}
	tb := table.GetTable()
	itemInfo := tb.TableItem.GetById(itemId)
	if itemInfo == nil {
		log.Error("[Player.AddItem] item id is illegal", log.Kv("itemId", itemId))
		return false
	}
	addResult := true
	if i.IsMoneyType(itemId) {
		addResult = i.player.AddMoney(itemId, addItemNum, operateReason)
	} else {
		addResult = i.player.AddBagItem(itemId, addItemNum, operateReason)
	}
	// 记录道具BI日志
	i.player.BIItemLog(itemInfo.Type, itemId, addItemNum, operateReason, 1, i.player.GetItemNumById(itemId))
	return addResult
}

// DelItem 通用接口：删除道具
// @Export
func (i *Item) DelItem(itemId int32, delItemNum int32, operateReason int32) bool {
	if itemId <= 0 || delItemNum <= 0 {
		log.Error("[Player.DelItem] invalid params",
			log.Kv("uid", i.player.Uid()),
			log.Kv("itemId", itemId),
			log.Kv("delItemNum", delItemNum),
			log.Kv("operateReason", operateReason))
		return false
	}
	tb := table.GetTable()
	itemInfo := tb.TableItem.GetById(itemId)
	if itemInfo == nil {
		log.Error("[Player.DelItem] item id is illegal", log.Kv("itemId", itemId))
		return false
	}
	addResult := true
	if i.IsMoneyType(itemId) {
		addResult = i.player.DelMoney(itemId, delItemNum, operateReason)
	} else {
		addResult = i.player.DelBagItem(itemId, delItemNum, operateReason)
	}
	// 记录道具BI日志
	i.player.BIItemLog(itemInfo.Type, itemId, delItemNum, operateReason, -1, i.player.GetItemNumById(itemId))
	return addResult
}

// DropItemToPlayer 通用接口：掉落专用接口
// @Export
func (i *Item) DropItemToPlayer(itemId int32, addItemNum int32, operateReason int32) bool {
	if itemId <= 0 || addItemNum <= 0 {
		log.Error("[Player.DropItemToPlayer] invalid params",
			log.Kv("uid", i.player.Uid()),
			log.Kv("itemId", itemId),
			log.Kv("addItemNum", addItemNum),
			log.Kv("operateReason", operateReason))
		return false
	}
	tb := table.GetTable()
	itemInfo := tb.TableItem.GetById(itemId)
	if itemInfo == nil {
		log.Error("[Player.DropItemToPlayer] item id is illegal", log.Kv("itemId", itemId))
		return false
	}
	return i.AddItem(itemId, addItemNum, operateReason)
}

// GetDropItemByDropID 通用接口：掉落道具接口
// @Export
func (i *Item) GetDropItemByDropID(dropId int32, operateReason int32) map[int32]int32 {
	resultItems := make(map[int32]int32)
	if dropId <= 0 {
		log.Error("[Player.GetDropItemByDropGroupID] invalid params",
			log.Kv("uid", i.player.Uid()),
			log.Kv("dropId", dropId),
			log.Kv("operateReason", operateReason))
		return resultItems
	}
	resultItems = i.player.GenDropItems(dropId)
	for itemId, itemNum := range resultItems {
		i.AddItem(itemId, itemNum, operateReason)
	}
	i.SendCommonDropItemList(resultItems, operateReason)
	return resultItems
}

// GetDropItemByDropGroupID 通用接口：掉落道具接口
// @Export
func (i *Item) GetDropItemByDropGroupID(dropGroupId int32, operateReason int32) map[int32]int32 {
	resultItems := make(map[int32]int32)
	if dropGroupId <= 0 {
		log.Error("[Player.GetDropItemByDropGroupID] invalid params",
			log.Kv("uid", i.player.Uid()),
			log.Kv("dropGroupId", dropGroupId),
			log.Kv("operateReason", operateReason))
		return resultItems
	}
	resultItems = i.player.GetDropItemsByGroupId(dropGroupId)
	for itemId, itemNum := range resultItems {
		i.AddItem(itemId, itemNum, operateReason)
	}
	i.SendCommonDropItemList(resultItems, operateReason)
	return resultItems
}

// SendCommonDropItemList 通用接口：发送掉落道具接口
// @Export
func (i *Item) SendCommonDropItemList(dropItemList map[int32]int32, operateReason int32) {
	// 构建最终消息
	msg := &cs.LCDropItemListRes{
		ItemInfo: []*public.PBDropItemDataInfo{},
		DropType: operateReason,
	}
	for dropItemId, dropItemNum := range dropItemList {
		msg.ItemInfo = append(msg.ItemInfo, &public.PBDropItemDataInfo{
			ItemId:    dropItemId,
			ItemCount: dropItemNum,
		})
	}
	// 将消息发送给客户端
	i.player.SendToClient(rpc_def.LCDropItemListRes, msg, false)
}

// UseItem 使用道具
// @Export
func (i *Item) UseItem(param *public.UseItemParam) error_code.Code {
	// 前置判断
	if param.Guid <= 0 || param.UseNum <= 0 {
		log.Error("[Item.UseItem] invalid params",
			log.Kv("uid", param.Guid),
			log.Kv("itemId", param.ItemId),
			log.Kv("UseNum", param.UseNum))
		return error_code.ERROR_PARAMS
	}
	if i.player.IsBagFull() == true {
		log.Error("[Item.UseItem] bag is full",
			log.Kv("uid", param.Guid),
			log.Kv("itemId", param.ItemId),
			log.Kv("UseNum", param.UseNum))
		return error_code.ERROR_PARAMS
	}
	// 条件判断
	itemNum := i.player.GetBagItemNumById(param.ItemId)
	if itemNum < param.UseNum {
		log.Error("[Item.UseItem] item num is not enough",
			log.Kv("uid", param.Guid),
			log.Kv("itemId", param.ItemId),
			log.Kv("UseNum", param.UseNum))
		return error_code.ERROR_PARAMS
	}
	// 扣除道具
	isDelSuccess := i.player.DelBagItem(param.ItemId, param.UseNum, 1)
	if isDelSuccess != true {
		log.Error("[Item.UseItem] item del fail",
			log.Kv("uid", param.Guid),
			log.Kv("itemId", param.ItemId),
			log.Kv("UseNum", param.UseNum))
		return error_code.ERROR_PARAMS
	}
	// 执行掉落逻辑
	tb := table.GetTable()
	itemInfo := tb.TableItem.GetById(param.ItemId)
	if itemInfo == nil {
		log.Error("[Item.UseItem] item id is illegal",
			log.Kv("uid", param.Guid),
			log.Kv("itemId", param.ItemId),
			log.Kv("UseNum", param.UseNum))
		return error_code.ERROR_PARAMS
	}
	itemDropId := itemInfo.NumParams[0]
	if itemDropId <= 0 {
		log.Error("[Item.UseItem] item drop id is illegal",
			log.Kv("uid", param.Guid),
			log.Kv("itemId", param.ItemId),
			log.Kv("UseNum", param.UseNum))
		return error_code.ERROR_PARAMS
	}
	i.GetDropItemByDropID(itemDropId, 4)

	return error_code.ERROR_OK
}
