package player

import (
	"liteframe/internal/common/table"
	"liteframe/internal/common/table/table_data"
	"liteframe/internal/game-logic/gameserver/logic/gameutil"
	"liteframe/pkg/log"
	"sort"
	"strconv"
	"strings"
)

// ItemType 掉落模块
//type ItemType int8
//
//const (
//	type_money ItemType = 0
//	type_item  ItemType = 1
//	type_equip ItemType = 2
//	type_skill ItemType = 3
//	type_none  ItemType = -1
//)

// BaseDropItem 掉落道具基础结构
/*
[掉落类型，ID（公式类型或道具ID），掉落权重（万分比），掉落数量基础值，掉落数量参数1(-1：表示受局内等级影响)，掉落数量参数2]
*/
type BaseDropItem struct {
	itemType int32
	id       int32
	weight   int32
	baseNum  int32
	param1   int32
	param2   int32
}

// ItemsDrop 一个掉落包结构 array[index][]=BaseDropItem
type ItemsDrop struct {
	items [][]*BaseDropItem
}
type Drop struct {
	player   *Player
	allDrops map[int32]*ItemsDrop //所有掉落map列表
}

// New 初始化掉落模块数据
func NewDrop(p *Player) *Drop {
	dropMgr := &Drop{player: p}
	dropMgr.FormatAllDropItems()
	return dropMgr
}

// GetDropItemsByGroupId
// @Export
func (d *Drop) GetDropItemsByGroupId(groupId int32) map[int32]int32 {
	resultItems := make(map[int32]int32)
	tb := table.GetTable()
	rowDropGroupData := tb.TableDropGroup.GetById(groupId)
	if rowDropGroupData == nil {
		log.Error("[Drop.rowDropGroupData] drop group not exist", log.Kv("groupId", groupId))
		return resultItems
	}
	var dropNum int32 = 1
	isDuplicate := true
	if rowDropGroupData.DropNum > 0 {
		dropNum = rowDropGroupData.DropNum
	}
	if rowDropGroupData.IsCanDuplicate <= 0 {
		isDuplicate = false
	}
	WeightGroupDropList := make(gameutil.WeightedSlice, 0)
	for idx, dropId := range rowDropGroupData.DropBoxID {
		WeightInfo := gameutil.Weighted{Item: dropId, Weight: int(rowDropGroupData.DropWeight[idx])}
		WeightGroupDropList = append(WeightGroupDropList, WeightInfo)
	}
	if len(WeightGroupDropList) == 0 {
		return resultItems
	}
	dropIdList := make([]int32, 0)
	for i := 0; i < int(dropNum); i++ {
		sort.Sort(WeightGroupDropList)
		//选择随机权重对象
		selectDropId, index := WeightGroupDropList.SelectRandom()
		if selectDropId.(int32) > 0 {
			dropIdList = append(dropIdList, selectDropId.(int32))
			//不能重复，删除已经随到的道具
			if !isDuplicate {
				if index+1 < len(WeightGroupDropList) { //删除的不是最后一个元素
					WeightGroupDropList = append(WeightGroupDropList[:index], WeightGroupDropList[index+1])
				} else {
					WeightGroupDropList = WeightGroupDropList[:len(WeightGroupDropList)-1]
				}
			}
		}
	}
	//对随机组里的所有dropId执行掉落逻辑
	for _, dropId := range dropIdList {
		retItemMap := d.GenDropItems(dropId)
		for itemId, itemNum := range retItemMap {
			resultItems[itemId] += itemNum
		}
	}
	return resultItems
}

// GenDropItems 生成真实的掉落道具map
// @Export
func (d *Drop) GenDropItems(dropBoxId int32) map[int32]int32 {
	resultItems := make(map[int32]int32)
	tb := table.GetTable()
	rowDropData := tb.TableDropBox.GetById(dropBoxId)
	if rowDropData == nil {
		log.Error("[Drop.GenDropItems] drop not exist", log.Kv("DropBoxId", dropBoxId))
		return resultItems
	}
	var dropNum int32 = 1
	isDuplicate := true
	if rowDropData.DropNum > 0 {
		dropNum = rowDropData.DropNum
	}
	if rowDropData.IsCanDuplicate <= 0 {
		isDuplicate = false
	}
	if tempDropItem, ok := d.allDrops[dropBoxId]; ok {
		for _, itemValue := range tempDropItem.items { //对每一个道具池子
			retItemList := d.DropRandom(itemValue, dropNum, isDuplicate)
			//整理道具列表，合并相同道具
			for _, retItem := range retItemList {
				resultItems[retItem.id] += retItem.baseNum
			}
		}
	} else {
		log.Error("[Drop.GenDropItems] drop item not exist", log.Kv("DropBoxId", dropBoxId))
	}
	return resultItems
}

// DropRandom 根据掉落数量，是否重复等信息随机掉落物
func (d *Drop) DropRandom(baseItemList []*BaseDropItem, num int32, IsCanDuplicate bool) []*BaseDropItem {
	WeightItemList := make(gameutil.WeightedSlice, 0)
	retItemList := make([]*BaseDropItem, 0)
	for _, BaseItem := range baseItemList {
		WeightInfo := gameutil.Weighted{Item: BaseItem, Weight: int(BaseItem.weight)}
		WeightItemList = append(WeightItemList, WeightInfo)
	}
	if len(WeightItemList) == 0 {
		return retItemList
	}
	for i := 0; i < int(num); i++ {
		//排序权重列表
		sort.Sort(WeightItemList)
		//选择随机权重对象
		selectedItem, index := WeightItemList.SelectRandom()
		if selectedItem != nil {
			retItemList = append(retItemList, selectedItem.(*BaseDropItem))
			//不能重复，删除已经随到的道具
			if !IsCanDuplicate {
				if index+1 < len(WeightItemList) { //删除的不是最后一个元素
					WeightItemList = append(WeightItemList[:index], WeightItemList[index+1])
				} else {
					WeightItemList = WeightItemList[:len(WeightItemList)-1]
				}
			}
		}
	}
	return retItemList
}

// FormatAllDropItems 序列化掉落表所有掉落数据到DropItems
func (d *Drop) FormatAllDropItems() {
	d.allDrops = make(map[int32]*ItemsDrop)
	tb := table.GetTable()
	tb.TableDropBox.Foreach(d.DropTableCallBack)
}
func (d *Drop) DropTableCallBack(rowDropBox *table_data.TableDropBox) bool {
	retDrop := new(ItemsDrop)
	for i := 0; i < 10; i++ {
		retStr := d.GetDropTableString(rowDropBox, int32(i))
		if retStr != "" {

			ret := d.parseStringToListArray(retStr, int32(i), retDrop)
			if !ret {
				retDrop = nil
			}
		}
	}
	if retDrop != nil && len(retDrop.items) > 0 {
		d.allDrops[rowDropBox.ID] = retDrop
	}
	return false
}

func (d *Drop) GetDropTableString(rowDropBox *table_data.TableDropBox, index int32) string {
	switch index {
	case 0:
		return rowDropBox.DropItem1
	case 1:
		return rowDropBox.DropItem2
	case 2:
		return rowDropBox.DropItem3
	case 3:
		return rowDropBox.DropItem4
	case 4:
		return rowDropBox.DropItem5
	case 5:
		return rowDropBox.DropItem6
	case 6:
		return rowDropBox.DropItem7
	case 7:
		return rowDropBox.DropItem8
	case 8:
		return rowDropBox.DropItem9
	case 9:
		return rowDropBox.DropItem10
	default:
		return ""
	}
}

// 将原始配置掉落字符串转换为DropItems结构
func (d *Drop) parseStringToListArray(dropBoxItem string, index int32, retDrop *ItemsDrop) bool {
	if dropBoxItem == "" || dropBoxItem == "-1" || dropBoxItem == "0" {
		return false
	}
	//log.Info("[Drop.parseStringToListArray] DropBox index", log.Kv("index", index))
	if !strings.HasPrefix(dropBoxItem, "[[") {
		log.Error("[Drop.parseStringToListArray] DropBox Prefix params error")
		return false
	}
	if !strings.HasSuffix(dropBoxItem, "]]") {
		log.Error("[Drop.parseStringToListArray] DropBox Suffix params error")
		return false
	}
	subDropBoxStr1 := strings.Replace(dropBoxItem, "[[", "", 1)
	subDropBoxStr2 := strings.Replace(subDropBoxStr1, "]]", "", 1)
	itemStrArray := strings.Split(subDropBoxStr2, "],[")
	itemList := make([]*BaseDropItem, 0)
	for _, items := range itemStrArray {
		baseItem := new(BaseDropItem)
		itemsElm := strings.Split(items, ",")
		//道具类型解析
		if len(itemsElm) > 0 {
			iType, err := strconv.Atoi(itemsElm[0])
			if err != nil {
				log.Error("[Drop.parseStringToListArray] Drop items type error type",
					log.Kv("type", itemsElm[0]))
				return false
			}
			baseItem.itemType = int32(iType)
		} else {
			log.Error("[Drop.parseStringToListArray] Drop items type nil")
			return false
		}
		//道具id解析
		if len(itemsElm) > 1 {
			id, err := strconv.Atoi(itemsElm[1])
			if err != nil {
				log.Error("[Drop.parseStringToListArray] Drop items id error",
					log.Kv("id", itemsElm[1]))
				return false
			}
			baseItem.id = int32(id)
		} else {
			log.Error("[Drop.parseStringToListArray] Drop items id nil")
			return false
		}
		//道具权重解析
		if len(itemsElm) > 2 {
			weight, err := strconv.Atoi(itemsElm[2])
			if err != nil {
				log.Error("[Drop.parseStringToListArray] Drop items weight error",
					log.Kv("weight", itemsElm[2]))
				return false
			}
			baseItem.weight = int32(weight)
		} else {
			log.Error("[Drop.parseStringToListArray] Drop items weight nil")
			return false
		}
		//道具数量解析
		if len(itemsElm) > 3 {
			num, err := strconv.Atoi(itemsElm[3])
			if err != nil {
				log.Error("[Drop.parseStringToListArray] Drop items basenum error",
					log.Kv("num", itemsElm[3]))
				return false
			}
			baseItem.baseNum = int32(num)
		} else {
			log.Error("[Drop.parseStringToListArray] Drop items basenum nil")
			return false
		}
		//道具参数1解析，可为空
		if len(itemsElm) > 4 {
			param1, err := strconv.Atoi(itemsElm[4])
			if err != nil {
				log.Error("[Drop.parseStringToListArray] Drop items param1 error",
					log.Kv("param1", itemsElm[4]))
				continue
			}
			baseItem.param1 = int32(param1)
		}
		//道具参数2解析，可为空
		if len(itemsElm) > 5 {
			param2, err := strconv.Atoi(itemsElm[5])
			if err != nil {
				log.Error("[Drop.parseStringToListArray] Drop items param2 error",
					log.Kv("param2", itemsElm[5]))
				continue
			}
			baseItem.param2 = int32(param2)
		}
		itemList = append(itemList, baseItem)
		retDrop.items = append(retDrop.items, itemList)
	}
	return true
}
